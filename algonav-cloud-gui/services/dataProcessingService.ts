import { createClient } from "@/utils/supabase/client";

interface Variable {
  data: string;
  name: string;
  links: any[];
}

interface VariableContainer {
  vars: Variable[];
}

interface Template {
  id: string;
  name: string;
}

interface Dataset {
  id: string;
  name: string;
}

const supabase = createClient();

const mergeVariables = (baseVars: VariableContainer, overrideVars: VariableContainer): VariableContainer => {
  if (!overrideVars?.vars) return baseVars;
  if (!baseVars?.vars) return overrideVars;

  const mergedVarsMap = new Map(
    baseVars.vars.map(v => [v.name, v])
  );

  overrideVars.vars.forEach(overrideVar => {
    mergedVarsMap.set(overrideVar.name, overrideVar);
  });

  return {
    vars: Array.from(mergedVarsMap.values())
  };
};

export async function processData(selectedTemplates: Template[], selectedDatasets: Dataset[], customJobName?: string) {
    const startTime = performance.now();
    console.log(`🚀 Starting job creation with ${selectedTemplates.length} templates and ${selectedDatasets.length} datasets`);

    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) { // Add check for null user
      console.error('Error fetching user or user is null:', userError);
      return; // Exit if no user
    }

    const userId = user.id; // Now safe to access user.id
    const tasks = [];

    // Step 1: Create Job
    const jobStartTime = performance.now();
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .insert({
        user_id: userId,
        name: customJobName || `Job ${new Date().toISOString()}`,
        description: `Job created for ${selectedTemplates.length} templates and ${selectedDatasets.length} datasets`,
        status: 'queued'
      })
      .select()
      .single();

    if (jobError) {
      console.error('Error creating job:', jobError);
      return;
    }

    const jobEndTime = performance.now();
    console.log(`✅ Job created in ${(jobEndTime - jobStartTime).toFixed(2)}ms (Job ID: ${job.id})`);

    // Step 2: Batch fetch all template data in one query
    const templatesStartTime = performance.now();
    const templateIds = selectedTemplates.map(t => t.id);
    console.log(`📋 Fetching ${templateIds.length} templates...`);

    const { data: templatesData, error: templatesError } = await supabase
      .from('global_job_templates')
      .select('id, template_data, vars')
      .in('id', templateIds);

    if (templatesError) {
      console.error('Error fetching templates data:', templatesError);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    // Create a map for quick template lookup
    const templateDataMap = new Map(templatesData.map(t => [t.id, t]));
    const templatesEndTime = performance.now();
    console.log(`✅ Templates fetched in ${(templatesEndTime - templatesStartTime).toFixed(2)}ms`);

    // Step 3: Batch fetch all dataset data in one query
    const datasetsStartTime = performance.now();
    const datasetIds = selectedDatasets.map(d => d.id);
    console.log(`📊 Fetching ${datasetIds.length} datasets...`);

    const { data: datasetsData, error: datasetsError } = await supabase
      .from('datasets')
      .select('id, variable_overrides')
      .in('id', datasetIds);

    if (datasetsError) {
      console.error('Error fetching datasets data:', datasetsError);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    // Create a map for quick dataset lookup
    const datasetDataMap = new Map(datasetsData.map(d => [d.id, d]));
    const datasetsEndTime = performance.now();
    console.log(`✅ Datasets fetched in ${(datasetsEndTime - datasetsStartTime).toFixed(2)}ms`);

    // Step 4: Batch fetch all dataset files in one query
    const filesStartTime = performance.now();
    console.log(`📁 Fetching dataset files for ${datasetIds.length} datasets...`);

    const { data: allDatasetFiles, error: datasetFilesError } = await supabase
      .from('dataset_files')
      .select('dataset_id, file_id, file_type')
      .in('dataset_id', datasetIds);

    if (datasetFilesError) {
      console.error('Error fetching dataset files:', datasetFilesError);
      await supabase.from('jobs').delete().eq('id', job.id);
      return;
    }

    // Group dataset files by dataset_id for quick lookup
    const datasetFilesMap = new Map<string, any[]>();
    allDatasetFiles.forEach(file => {
      const datasetId = file.dataset_id.toString();
      if (!datasetFilesMap.has(datasetId)) {
        datasetFilesMap.set(datasetId, []);
      }
      datasetFilesMap.get(datasetId)!.push(file);
    });

    const filesEndTime = performance.now();
    console.log(`✅ Dataset files fetched in ${(filesEndTime - filesStartTime).toFixed(2)}ms (${allDatasetFiles.length} files total)`);

    // Step 5: Process all combinations in memory (much faster)
    const processingStartTime = performance.now();
    const totalCombinations = selectedTemplates.length * selectedDatasets.length;
    console.log(`⚙️ Processing ${totalCombinations} template-dataset combinations...`);

    for (const template of selectedTemplates) {
      const templateData = templateDataMap.get(template.id);
      if (!templateData) {
        console.error(`Template data not found for template ${template.id}`);
        continue;
      }

      for (const dataset of selectedDatasets) {
        const datasetData = datasetDataMap.get(dataset.id);
        if (!datasetData) {
          console.error(`Dataset data not found for dataset ${dataset.id}`);
          continue;
        }

        const datasetFiles = datasetFilesMap.get(dataset.id.toString()) || [];

        // Merge variables with priority: template < category (future) < dataset
        let finalVars = templateData.vars as VariableContainer;

        // Here you can add category vars merging when implemented
        // finalVars = mergeVariables(finalVars, categoryVars);

        // Merge dataset overrides
        finalVars = mergeVariables(finalVars, datasetData.variable_overrides);

        // Ensure finalVars and finalVars.vars exist
        if (!finalVars) {
          finalVars = { vars: [] };
        }
        if (!finalVars.vars) {
          finalVars.vars = [];
        }

        // Add or update DSNAME variable using dataset.name
        const dsnameVarIndex = finalVars.vars.findIndex(v => v.name === 'DSNAME');
        if (dsnameVarIndex > -1) {
          // Update existing DSNAME
          finalVars.vars[dsnameVarIndex].data = dataset.name;
        } else {
          // Add new DSNAME
          finalVars.vars.push({ name: 'DSNAME', data: dataset.name, links: [] });
        }

        // Define the type for the accumulator explicitly
        const fileTypeMap = datasetFiles.reduce((acc: Record<string, string[]>, file) => {
          const fileType = file.file_type as string; // Assert file_type as string if needed, or handle null/undefined
          const fileId = file.file_id as string;   // Assert file_id as string if needed

          if (!acc[fileType]) {
            acc[fileType] = [];
          }
          acc[fileType].push(fileId);
          return acc;
        }, {} as Record<string, string[]>); // Initialize with the correct type

        const workervars = Object.entries(fileTypeMap).map(([fileType, fileIds]) => ({
          name: fileType,
          file_ids: fileIds
        }));

        tasks.push({
          user_id: userId,
          name: `${template.name} - ${dataset.name}`,
          description: `Task created from template ${template.name} for dataset ${dataset.name}`,
          global_job_template_id: template.id,
          dataset_id: dataset.id,
          job_json: templateData.template_data,
          vars: finalVars,
          workervars: workervars,
          job_id: job.id,
          status: 'queued'
        });
      }
    }

    const processingEndTime = performance.now();
    console.log(`✅ Task data processed in ${(processingEndTime - processingStartTime).toFixed(2)}ms (${tasks.length} tasks created)`);

    // Step 6: Batch insert for all tasks (with chunking for large datasets)
    const insertStartTime = performance.now();
    console.log(`💾 Inserting ${tasks.length} tasks into database...`);

    const BATCH_SIZE = 1000; // Supabase limit is around 1000 rows per insert
    let insertedCount = 0;

    if (tasks.length <= BATCH_SIZE) {
      // Single insert for smaller batches
      const { data, error } = await supabase.from('tasks').insert(tasks);
      if (error) {
        console.error('Error creating tasks:', error);
        await supabase.from('jobs').delete().eq('id', job.id);
        return;
      }
      insertedCount = tasks.length;
    } else {
      // Chunked inserts for larger batches
      console.log(`📦 Large batch detected, splitting into chunks of ${BATCH_SIZE}...`);

      for (let i = 0; i < tasks.length; i += BATCH_SIZE) {
        const chunk = tasks.slice(i, i + BATCH_SIZE);
        const chunkNumber = Math.floor(i / BATCH_SIZE) + 1;
        const totalChunks = Math.ceil(tasks.length / BATCH_SIZE);

        console.log(`💾 Inserting chunk ${chunkNumber}/${totalChunks} (${chunk.length} tasks)...`);

        const { data, error } = await supabase.from('tasks').insert(chunk);
        if (error) {
          console.error(`Error creating tasks in chunk ${chunkNumber}:`, error);
          await supabase.from('jobs').delete().eq('id', job.id);
          return;
        }
        insertedCount += chunk.length;
      }
    }

    const insertEndTime = performance.now();
    console.log(`✅ ${insertedCount} tasks inserted in ${(insertEndTime - insertStartTime).toFixed(2)}ms`);

    const totalEndTime = performance.now();
    console.log(`🎉 Job creation completed in ${(totalEndTime - startTime).toFixed(2)}ms total`);
    console.log(`📊 Performance breakdown:
    - Job creation: ${(jobEndTime - jobStartTime).toFixed(2)}ms
    - Templates fetch: ${(templatesEndTime - templatesStartTime).toFixed(2)}ms
    - Datasets fetch: ${(datasetsEndTime - datasetsStartTime).toFixed(2)}ms
    - Files fetch: ${(filesEndTime - filesStartTime).toFixed(2)}ms
    - Processing: ${(processingEndTime - processingStartTime).toFixed(2)}ms
    - Database insert: ${(insertEndTime - insertStartTime).toFixed(2)}ms`);

    return { tasks: tasks, job: job };
}
